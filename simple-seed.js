// Simple seed script to create basic data for testing Ballerine
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function simpleSeed() {
  console.log('Starting simple seed...');

  try {
    // Create a project
    const project = await prisma.project.upsert({
      where: { id: 'default-project' },
      update: {},
      create: {
        id: 'default-project',
        name: 'Default Project',
        config: {}
      }
    });

    console.log('Created project:', project.id);

    // Create a customer
    const customer = await prisma.customer.upsert({
      where: { id: 'default-customer' },
      update: {},
      create: {
        id: 'default-customer',
        name: 'Default Customer',
        displayName: 'Default Customer',
        logoImageUri: '',
        faviconImageUri: '',
        customerStatus: 'active',
        country: 'US',
        language: 'en',
        config: {},
        authenticationConfiguration: {},
        projectId: project.id
      }
    });

    console.log('Created customer:', customer.id);

    // Create an admin user
    const hashedPassword = await bcrypt.hash('admin', 10);
    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        id: 'admin-user',
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        password: hashedPassword,
        roles: ['admin'],
        projectId: project.id
      }
    });

    console.log('Created user:', user.email);

    // Create workflow runtime data tokens for testing
    const kybToken = await prisma.workflowRuntimeDataToken.upsert({
      where: { token: '12345678-1234-1234-1234-123456789012' },
      update: {},
      create: {
        id: 'kyb-token',
        token: '12345678-1234-1234-1234-123456789012',
        projectId: project.id
      }
    });

    const kycToken = await prisma.workflowRuntimeDataToken.upsert({
      where: { token: '12345678-1234-1234-1234-123456789000' },
      update: {},
      create: {
        id: 'kyc-token',
        token: '12345678-1234-1234-1234-123456789000',
        projectId: project.id
      }
    });

    console.log('Created tokens:', kybToken.token, kycToken.token);

    console.log('Simple seed completed successfully!');
  } catch (error) {
    console.error('Error during seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

simpleSeed();
