version: '3'
services:
  ballerine-case-managment:
    container_name: backoffice
    image: ghcr.io/ballerine-io/backoffice:dev
    platform: linux/amd64
    ports:
      - ${BACKOFFICE_PORT}:80
    depends_on:
      - ballerine-workflow-service
    restart: on-failure
    environment:
      VITE_DOMAIN: ${VITE_DOMAIN}
      VITE_API_KEY: ${VITE_API_KEY}
      VITE_AUTH_ENABLED: ${VITE_AUTH_ENABLED}
      VITE_MOCK_SERVER: ${VITE_MOCK_SERVER}
      VITE_ENVIRONMENT_NAME: ${VITE_ENVIRONMENT_NAME}
      VITE_POLLING_INTERVAL: ${VITE_POLLING_INTERVAL}
      VITE_ASSIGNMENT_POLLING_INTERVAL: ${VITE_ASSIGNMENT_POLLING_INTERVAL}
      VITE_FETCH_SIGNED_URL: ${VITE_FETCH_SIGNED_URL}

  ballerine-kyb-app:
    container_name: kyb-app
    platform: linux/amd64
    image: "ghcr.io/ballerine-io/kyb-app:dev"
    ports:
      - ${KYB_APP_PORT}:80
    depends_on:
      - ballerine-workflow-service
    restart: on-failure
    environment:
      VITE_DOMAIN: ${VITE_DOMAIN}
      VITE_ENVIRONMENT_NAME: ${VITE_ENVIRONMENT_NAME}
      VITE_KYB_DEFINITION_ID: ${VITE_KYB_DEFINITION_ID}
  ballerine-workflow-service:
    container_name: workflow-service
    platform: linux/amd64
    image: "ghcr.io/ballerine-io/workflows-service:dev"
    command:
      - /bin/sh
      - -c
      - |
        npm run db:init
        npm run seed
        dumb-init npm run prod
    ports:
      - ${WORKFLOW_SVC_PORT}:${WORKFLOW_SVC_PORT}
    environment:
      BCRYPT_SALT: ${BCRYPT_SALT}
      SESSION_EXPIRATION_IN_MINUTES: ${SESSION_EXPIRATION_IN_MINUTES}
      DB_URL: ************************************/postgres
      API_KEY: ${API_KEY}
      NODE_ENV: ${NODE_ENV}
      COMPOSE_PROJECT_NAME: ${COMPOSE_PROJECT_NAME}
      DB_PORT: ${DB_PORT}
      DB_USER: ${DB_USER}
      DB_PASSWORD: admin
      SESSION_SECRET: ${SESSION_SECRET}
      BACKOFFICE_CORS_ORIGIN: http://${DOMAIN_NAME:-localhost}:${BACKOFFICE_PORT}
      WORKFLOW_DASHBOARD_CORS_ORIGIN: http://${DOMAIN_NAME:-localhost}:${WORKFLOW_DASHBOARD_PORT}
      PORT: ${WORKFLOW_SVC_PORT}
      KYB_EXAMPLE_CORS_ORIGIN: http://${DOMAIN_NAME:-localhost}:${KYB_APP_PORT}
      APP_API_URL: https://alon.ballerine.dev
      EMAIL_API_TOKEN: ''
      EMAIL_API_URL: https://api.sendgrid.com/v3/mail/send
      UNIFIED_API_URL: 'https://unified-api-test.eu.ballerine.app'
      UNIFIED_API_TOKEN: ''
      UNIFIED_API_SHARED_SECRET: ''
      ENVIRONMENT_NAME: 'development'
      HASHING_KEY_SECRET: ${HASHING_KEY_SECRET}
      HASHING_KEY_SECRET_BASE64: ${HASHING_KEY_SECRET_BASE64}
      NOTION_API_KEY: ${NOTION_API_KEY}
    depends_on:
      ballerine-postgres:
        condition: service_healthy
    restart: on-failure
  ballerine-workflows-dashboard:
    container_name: workflows-dashboard
    platform: linux/amd64
    image: ghcr.io/ballerine-io/workflows-dashboard:dev
    ports:
      - ${WORKFLOW_DASHBOARD_PORT}:80
    environment:
      VITE_DOMAIN: ${VITE_DOMAIN}
      VITE_ENVIRONMENT_NAME: ${VITE_ENVIRONMENT_NAME}
      MODE: ${MODE}
      VITE_IMAGE_LOGO_URL: ${VITE_IMAGE_LOGO_URL}
    depends_on:
      - ballerine-workflow-service
    restart: on-failure
  ballerine-postgres:
    container_name: postgres
    image: sibedge/postgres-plv8:15.3-3.1.7
    ports:
      - ${DB_PORT}:${DB_PORT}
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin
    volumes:
      - postgres15:/var/lib/postgresql/data
    healthcheck:
      test:
        - CMD
        - pg_isready
        - -U
        - admin
      timeout: 45s
      interval: 10s
      retries: 10
volumes:
  postgres15: ~
