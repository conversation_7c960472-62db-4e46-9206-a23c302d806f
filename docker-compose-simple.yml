version: '3.8'

services:
  postgres:
    image: sibedge/postgres-plv8:15.3-3.1.7
    container_name: postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin
      POSTGRES_DB: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin"]
      interval: 30s
      timeout: 10s
      retries: 5

  workflow-service:
    image: ghcr.io/ballerine-io/workflows-service:dev
    container_name: workflow-service
    ports:
      - "3000:3000"
    environment:
      DB_URL: ************************************/postgres
      API_KEY: secret
      ADMIN_API_KEY: admin_secret
      NODE_ENV: development
      PORT: 3000
      BCRYPT_SALT: 10
      SESSION_SECRET: iGdnj4A0YOhj8dHJK7IWSvQKEZsG7P70FFehuddhFPjtg/bSkzFejYILk4Xue6Ilx9y3IAwzR8pV1gb4
      SESSION_EXPIRATION_IN_MINUTES: 60
      BACKOFFICE_CORS_ORIGIN: http://localhost:5137
      KYB_EXAMPLE_CORS_ORIGIN: http://localhost:5201
      KYC_EXAMPLE_CORS_ORIGIN: http://localhost:5202
      WORKFLOW_DASHBOARD_CORS_ORIGIN: http://localhost:5200
      HASHING_KEY_SECRET_BASE64: JDJiJDEwJFRYNjhmQi5JMlRCWHN0aGowakFHSi4=
      ENVIRONMENT_NAME: development
      COLLECTION_FLOW_URL: http://localhost:5201
      WEB_UI_SDK_URL: http://localhost:5202
      APP_API_URL: http://localhost:3000
      UNIFIED_API_URL: "http://localhost:3001"
      UNIFIED_API_TOKEN: "disabled"
      UNIFIED_API_SHARED_SECRET: "disabled"
      NOTION_API_KEY: secret
      EMAIL_API_TOKEN: ""
      EMAIL_API_URL: ""
      MAGIC_LINK_AUTH_JWT_SECRET: a-string-secret-at-least-256-bits-long
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped

# Commented out for now - we'll use the headless example instead
#  backoffice:
#    image: ghcr.io/ballerine-io/backoffice:dev
#    container_name: backoffice
#    ports:
#      - "5137:80"
#    environment:
#      VITE_API_URL: http://localhost:3000/api/v1/
#      VITE_API_KEY: secret
#    depends_on:
#      - workflow-service
#
#  kyb-app:
#    image: ghcr.io/ballerine-io/kyb-app:dev
#    container_name: kyb-app
#    ports:
#      - "5201:80"
#    environment:
#      VITE_API_URL: http://localhost:3000/api/v1/
#      VITE_API_KEY: secret
#    depends_on:
#      - workflow-service
#
#  workflows-dashboard:
#    image: ghcr.io/ballerine-io/workflows-dashboard:dev
#    container_name: workflows-dashboard
#    ports:
#      - "5200:80"
#    environment:
#      VITE_API_URL: http://localhost:3000/api/v1/
#      VITE_API_KEY: secret
#    depends_on:
#      - workflow-service

volumes:
  postgres_data:
